export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      invalid_scans: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          qr_data: string
          scan_type: string | null
          scanner_id: number | null
          scanner_name: string | null
          status: string | null
          timestamp: string
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          qr_data: string
          scan_type?: string | null
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp: string
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          qr_data?: string
          scan_type?: string | null
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp?: string
        }
        Relationships: []
      }
      notes: {
        Row: {
          created_at: string | null
          id: number
          note: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          note?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          note?: string | null
        }
        Relationships: []
      }
      scanners: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          location: string | null
          mode: string
          name: string
          password_hash: string
          requires_validation: boolean
          updated_at: string | null
          username: string
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          location?: string | null
          mode?: string
          name: string
          password_hash: string
          requires_validation?: boolean
          updated_at?: string | null
          username: string
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          location?: string | null
          mode?: string
          name?: string
          password_hash?: string
          requires_validation?: boolean
          updated_at?: string | null
          username?: string
        }
        Relationships: []
      }
      scans: {
        Row: {
          created_at: string | null
          gate: string | null
          id: number
          processed: boolean | null
          qr_data: string
          scan_signature: string | null
          scan_type: string
          scanner_id: number | null
          scanner_name: string | null
          status: string | null
          timestamp: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          gate?: string | null
          id?: number
          processed?: boolean | null
          qr_data: string
          scan_signature?: string | null
          scan_type: string
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          gate?: string | null
          id?: number
          processed?: boolean | null
          qr_data?: string
          scan_signature?: string | null
          scan_type?: string
          scanner_id?: number | null
          scanner_name?: string | null
          status?: string | null
          timestamp?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scans_scanner_id_fkey"
            columns: ["scanner_id"]
            isOneToOne: false
            referencedRelation: "scanners"
            referencedColumns: ["id"]
          },
        ]
      }
      workforce: {
        Row: {
          added_at: string | null
          badge_id: string | null
          category: string | null
          converted_url: string | null
          created_at: string
          designation: string | null
          designation_2: string | null
          email: string | null
          email_sent_at: string | null
          email_status: string | null
          fa_acc: boolean | null
          fa_com: boolean | null
          fa_crl: boolean | null
          fa_eva: boolean | null
          fa_fbs: boolean | null
          fa_fnp: boolean | null
          fa_gmh: boolean | null
          fa_log: boolean | null
          fa_mme: boolean | null
          fa_ogc: boolean | null
          fa_prg: boolean | null
          fa_tml: boolean | null
          fa_wkf: boolean | null
          first_name: string | null
          functional_area: string | null
          id: number
          last_name: string | null
          local_stored_url: string | null
          mobile: string | null
          organization: string | null
          profile_url_raw: string | null
          qr_data: string
          tshirt_size: string | null
          updated_at: string | null
          whatsapp: string | null
          zone_1: boolean | null
          zone_2: boolean | null
          zone_3: boolean | null
          zone_4: boolean | null
          zone_5: boolean | null
          zone_6: boolean | null
          zones: Json | null
        }
        Insert: {
          added_at?: string | null
          badge_id?: string | null
          category?: string | null
          converted_url?: string | null
          created_at?: string
          designation?: string | null
          designation_2?: string | null
          email?: string | null
          email_sent_at?: string | null
          email_status?: string | null
          fa_acc?: boolean | null
          fa_com?: boolean | null
          fa_crl?: boolean | null
          fa_eva?: boolean | null
          fa_fbs?: boolean | null
          fa_fnp?: boolean | null
          fa_gmh?: boolean | null
          fa_log?: boolean | null
          fa_mme?: boolean | null
          fa_ogc?: boolean | null
          fa_prg?: boolean | null
          fa_tml?: boolean | null
          fa_wkf?: boolean | null
          first_name?: string | null
          functional_area?: string | null
          id?: number
          last_name?: string | null
          local_stored_url?: string | null
          mobile?: string | null
          organization?: string | null
          profile_url_raw?: string | null
          qr_data: string
          tshirt_size?: string | null
          updated_at?: string | null
          whatsapp?: string | null
          zone_1?: boolean | null
          zone_2?: boolean | null
          zone_3?: boolean | null
          zone_4?: boolean | null
          zone_5?: boolean | null
          zone_6?: boolean | null
          zones?: Json | null
        }
        Update: {
          added_at?: string | null
          badge_id?: string | null
          category?: string | null
          converted_url?: string | null
          created_at?: string
          designation?: string | null
          designation_2?: string | null
          email?: string | null
          email_sent_at?: string | null
          email_status?: string | null
          fa_acc?: boolean | null
          fa_com?: boolean | null
          fa_crl?: boolean | null
          fa_eva?: boolean | null
          fa_fbs?: boolean | null
          fa_fnp?: boolean | null
          fa_gmh?: boolean | null
          fa_log?: boolean | null
          fa_mme?: boolean | null
          fa_ogc?: boolean | null
          fa_prg?: boolean | null
          fa_tml?: boolean | null
          fa_wkf?: boolean | null
          first_name?: string | null
          functional_area?: string | null
          id?: number
          last_name?: string | null
          local_stored_url?: string | null
          mobile?: string | null
          organization?: string | null
          profile_url_raw?: string | null
          qr_data?: string
          tshirt_size?: string | null
          updated_at?: string | null
          whatsapp?: string | null
          zone_1?: boolean | null
          zone_2?: boolean | null
          zone_3?: boolean | null
          zone_4?: boolean | null
          zone_5?: boolean | null
          zone_6?: boolean | null
          zones?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
  | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
    Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
  ? R
  : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
    DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
    DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R
    }
  ? R
  : never
  : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Insert: infer I
  }
  ? I
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Insert: infer I
  }
  ? I
  : never
  : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
  | keyof DefaultSchema["Tables"]
  | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
  : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
    Update: infer U
  }
  ? U
  : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
    Update: infer U
  }
  ? U
  : never
  : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
  | keyof DefaultSchema["Enums"]
  | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
  : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
  | keyof DefaultSchema["CompositeTypes"]
  | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
  ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
  : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const
