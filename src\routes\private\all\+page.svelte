<script lang="ts">
	import type { PageData } from './$types';
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table';
	import { goto } from '$app/navigation';

	let { data }: { data: PageData } = $props();

	function openImageUrl(url: string) {
		if (url) {
			window.open(url, '_blank');
		}
	}
</script>

<!-- <div class="container mx-auto p-4"> -->
<div class=" mx-auto p-4">
	<div class="flex flex-col gap-4">
		<div class="flex justify-between items-center">
			<h1 class="text-2xl font-bold">All Badges</h1>
			<Button disabled onclick={sendEmailToAll} variant="default">Send Email to All</Button>
		</div>

		<div class="rounded-lg border">
			<Table.Root>
				<Table.Header class="bg-muted">
					<Table.Row>
						<Table.Head class="border-r">#</Table.Head>

						<Table.Head>First Name</Table.Head>
						<Table.Head>Last Name</Table.Head>
						<Table.Head>Email</Table.Head>
						<Table.Head>WhatsApp 2</Table.Head>
						<Table.Head>QR Data</Table.Head>
						<Table.Head>Category</Table.Head>
						<Table.Head>Email Status</Table.Head>
						<Table.Head>Email Sent At</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each data.guests as guest, index}
						<Table.Row>
							<Table.Cell class="border-r bg-muted">{index + 1}</Table.Cell>

							<Table.Cell>{guest.first_name}</Table.Cell>
							<Table.Cell>{guest.last_name}</Table.Cell>
							<Table.Cell>{guest.email}</Table.Cell>
							<Table.Cell>{guest.whatsapp}</Table.Cell>
							<Table.Cell>{guest.qr_data}</Table.Cell>
							<Table.Cell>{guest.category}</Table.Cell>
							<Table.Cell>{guest.email_status}</Table.Cell>
							<Table.Cell>{guest.email_sent_at}</Table.Cell>

							<Table.Cell>
								<Button size="sm" onclick={() => sendSingleEmail(guest)}>Send Email</Button>
								<Button size="sm" onclick={() => goto(`/private/all/edit/${guest.id}`)}>Edit</Button
								>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		</div>
	</div>
</div>
