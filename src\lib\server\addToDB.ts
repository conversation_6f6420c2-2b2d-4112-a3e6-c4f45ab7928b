
import { supabaseAdmin } from './db/supabaseAdmin';
import { randomBytes } from 'crypto';

type RowResult = {
    row: number;
    status: 'success' | 'fail';
    message: string;
};

/**
 * Splits a full name into first and last name
 * @param fullName - The full name to split
 * @returns Object with firstName and lastName properties
 */
function splitFullName(fullName: string): { firstName: string; lastName: string } {
    if (!fullName || typeof fullName !== 'string') {
        return { firstName: '', lastName: '' };
    }

    const trimmedName = fullName.trim();
    if (!trimmedName) {
        return { firstName: '', lastName: '' };
    }

    const nameParts = trimmedName.split(/\s+/);

    if (nameParts.length === 1) {
        // Only one name provided, treat as first name
        return { firstName: nameParts[0], lastName: '' };
    } else if (nameParts.length === 2) {
        // Two names: first and last
        return { firstName: nameParts[0], lastName: nameParts[1] };
    } else {
        // Multiple names: first name is the first part, last name is everything else
        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(' ');
        return { firstName, lastName };
    }
}

/**
 * Generates a unique QR data string
 * @param prefix - Optional prefix for the QR data (default: 'QR')
 * @returns A unique QR data string
 */
function generateQRData(prefix: string = 'QR'): string {
    const timestamp = Date.now().toString(36); // Base36 timestamp
    const randomPart = randomBytes(4).toString('hex').toUpperCase(); // 8 character hex
    return `${prefix}${timestamp}${randomPart}`;
}

/**
 * Checks if QR data already exists in the database
 * @param qrData - The QR data to check
 * @returns Promise<boolean> - true if exists, false if unique
 */
async function qrDataExists(qrData: string): Promise<boolean> {
    const { data, error } = await supabaseAdmin
        .from('workforce')
        .select('qr_data')
        .eq('qr_data', qrData)
        .limit(1);

    if (error) {
        console.error('Error checking QR data uniqueness:', error);
        return true; // Assume exists to be safe
    }

    return data && data.length > 0;
}

/**
 * Generates a unique QR data that doesn't exist in the database
 * @param prefix - Optional prefix for the QR data
 * @param maxAttempts - Maximum number of attempts to generate unique QR data
 * @returns Promise<string> - A unique QR data string
 */
async function generateUniqueQRData(prefix: string = 'QR', maxAttempts: number = 10): Promise<string> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        const qrData = generateQRData(prefix);
        const exists = await qrDataExists(qrData);

        if (!exists) {
            return qrData;
        }

        console.log(`QR data collision detected (attempt ${attempt}): ${qrData}`);
    }

    // If we still haven't found a unique one, add more randomness
    const timestamp = Date.now().toString(36);
    const randomPart = randomBytes(8).toString('hex').toUpperCase(); // 16 character hex
    const fallbackQR = `${prefix}${timestamp}${randomPart}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    console.warn(`Using fallback QR data after ${maxAttempts} attempts: ${fallbackQR}`);
    return fallbackQR;
}

export async function addToDB(csvData: any[]) {

    let successCount = 0;
    let failedCount = 0;

    const processedRows: RowResult[] = [];

    console.log('CSV Data received on addToDB.ts:', csvData);


    // Process each row
    // for (const row of csvData) {
    for (const [index, row] of csvData.entries()) {

        // Split fullname into first and last name
        const { firstName, lastName } = splitFullName(row.fullname || '');
        let capitalizedFirstName = firstName.toUpperCase();
        let capitalizedLastName = lastName.toUpperCase();

        let capitalizedCategory = row.category.toUpperCase() || '';


        // Generate unique QR data
        const uniqueQRData = await generateUniqueQRData('ICC');
        console.log(`Generated QR data for row ${index}: ${uniqueQRData}`);

        // try {
        // perform DB insert logic here


        const { error } = await supabaseAdmin.from('workforce').insert({
            added_at: row.timestamp,
            first_name: capitalizedFirstName,
            last_name: capitalizedLastName,
            mobile: row.mobile,
            whatsapp: row.whatsapp,
            email: row.emailaddress,
            organization: row.organization,
            tshirt_size: row.tshirtsize,
            qr_data: uniqueQRData,
            category: capitalizedCategory,

        });

        // console.log(`Data inserted successfully: ${index}': ${data}`);

        if (error) {
            // throw new Error(error.message);
            failedCount++;
            processedRows.push({ row: index, status: 'fail', message: error.message || 'Unknown error' });
        }

        successCount++;
        processedRows.push({ row: index, status: 'success', message: 'Inserted successfully' });
        // } catch (error) {
        //     failedCount++;
        //     processedRows.push({ row: index, status: 'fail', message: error || 'Unknown error' });
        // }
    }


    return {
        total: csvData.length,
        successCount,
        failedCount,
        rows: processedRows
    };
}

